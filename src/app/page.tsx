import {
  HeroSection,
  PopularCategoriesSection,
  FeaturedJobsSection,
  TestimonialsSection,
  CompanyLogosSection,
  StatisticsSection,
  RecentNewsSection,
  MobileAppSection,
  NewsletterSection,
} from "@/components/sections";

// Mock data for job listings
const mockJobs = [
  {
    id: "1",
    title: "Software Engineer (Android), Libraries",
    description:
      "We are looking for an experienced Android developer to join our team.",
    summary:
      "Join our team to create incredible Android experiences for millions of users.",
    requirements: [
      "5+ years of Android development",
      "Kotlin experience",
      "Experience with modern architecture patterns",
    ],
    benefits: ["Competitive salary", "Health insurance", "Flexible work hours"],
    location: "London, UK",
    workType: "remote" as const,
    employmentType: "full-time" as const,
    experienceLevel: "senior" as const,
    salary: {
      min: 35000,
      max: 45000,
      currency: "$",
      period: "yearly" as const,
    },
    company: {
      id: "c1",
      name: "Segment",
      logo: "/company-logos/segment.svg",
      description: "Leading analytics company",
      website: "https://segment.com",
      size: "501-1000",
      industry: "Technology",
      location: "London, UK",
      rating: 4.8,
    },
    skills: ["Android", "Kotlin", "Java", "RxJava", "MVVM"],
    tags: ["Android", "Mobile", "Software Development"],
    postedAt: "2023-07-20T10:00:00Z",
    deadline: "2023-08-20T10:00:00Z",
    applicationsCount: 25,
    isBookmarked: true,
    featured: true,
    urgent: true,
    status: "active" as const,
  },
  {
    id: "2",
    title: "Frontend Developer",
    description:
      "Frontend developer with React experience needed for a fast growing startup.",
    summary:
      "Join our team to build modern web applications using React and Next.js.",
    requirements: [
      "3+ years of React development",
      "TypeScript knowledge",
      "Experience with modern frontend frameworks",
    ],
    benefits: ["Competitive salary", "Remote work", "Learning budget"],
    location: "Berlin, Germany",
    workType: "hybrid" as const,
    employmentType: "full-time" as const,
    experienceLevel: "mid" as const,
    salary: {
      min: 50000,
      max: 65000,
      currency: "€",
      period: "yearly" as const,
    },
    company: {
      id: "c2",
      name: "TechStart",
      logo: "/company-logos/techstart.svg",
      description: "Innovative tech startup",
      website: "https://techstart.io",
      size: "11-50",
      industry: "Technology",
      location: "Berlin, Germany",
      rating: 4.5,
    },
    skills: ["React", "TypeScript", "Next.js", "CSS", "HTML"],
    tags: ["Frontend", "React", "Web Development"],
    postedAt: "2023-07-18T14:30:00Z",
    deadline: "2023-08-18T14:30:00Z",
    applicationsCount: 42,
    isBookmarked: false,
    featured: false,
    urgent: false,
    status: "active" as const,
  },
  {
    id: "3",
    title: "UX/UI Designer",
    description:
      "We're looking for a UX/UI Designer to create amazing user experiences.",
    summary:
      "Design beautiful and intuitive interfaces for web and mobile applications.",
    requirements: [
      "3+ years of UX/UI design experience",
      "Proficiency with Figma or similar tools",
      "Portfolio showcasing previous work",
    ],
    benefits: [
      "Competitive salary",
      "Flexible schedule",
      "Creative environment",
    ],
    location: "New York, USA",
    workType: "onsite" as const,
    employmentType: "contract" as const,
    experienceLevel: "senior" as const,
    salary: {
      min: 80000,
      max: 100000,
      currency: "$",
      period: "yearly" as const,
    },
    company: {
      id: "c3",
      name: "DesignHub",
      logo: "/company-logos/designhub.svg",
      description: "Creative design agency",
      website: "https://designhub.co",
      size: "51-200",
      industry: "Design",
      location: "New York, USA",
      rating: 4.7,
    },
    skills: ["UI Design", "UX Design", "Figma", "Prototyping", "User Research"],
    tags: ["Design", "UX", "UI"],
    postedAt: "2023-07-15T09:15:00Z",
    deadline: "2023-08-15T09:15:00Z",
    applicationsCount: 18,
    isBookmarked: false,
    featured: true,
    urgent: false,
    status: "active" as const,
  },
];

export default function Home() {
  return (
    <main className="min-h-screen">
      <HeroSection />
      <PopularCategoriesSection />
      <FeaturedJobsSection jobs={mockJobs} />
      <TestimonialsSection />
      <CompanyLogosSection />
      <StatisticsSection />
      <RecentNewsSection />
      <MobileAppSection />
      <NewsletterSection />
    </main>
  );
}
