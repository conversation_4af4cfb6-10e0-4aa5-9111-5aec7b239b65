import { Card, CardContent } from "@/components/ui/card";
import {
  Palette,
  TrendingUp,
  Code,
  Megaphone,
  DollarSign,
  Camera,
} from "lucide-react";

const categories = [
  {
    id: "design-creative",
    title: "Design & Creative",
    jobCount: "2,000+",
    icon: Palette,
    color: "from-purple-500 to-pink-500",
  },
  {
    id: "sales-marketing",
    title: "Sales & Marketing",
    jobCount: "1,500+",
    icon: TrendingUp,
    color: "from-blue-500 to-cyan-500",
  },
  {
    id: "development-it",
    title: "Development & IT",
    jobCount: "3,200+",
    icon: Code,
    color: "from-green-500 to-emerald-500",
  },
  {
    id: "digital-marketing",
    title: "Digital Marketing",
    jobCount: "800+",
    icon: Megaphone,
    color: "from-orange-500 to-red-500",
  },
  {
    id: "finance-accounting",
    title: "Finance & Accounting",
    jobCount: "1,200+",
    icon: DollarSign,
    color: "from-indigo-500 to-purple-500",
  },
  {
    id: "photography",
    title: "Photography",
    jobCount: "600+",
    icon: Camera,
    color: "from-pink-500 to-rose-500",
  },
];

export function PopularCategoriesSection() {
  return (
    <section className="py-16 lg:py-24 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-[#202124] mb-4">
            Popular Job Categories
          </h2>
          <p className="text-lg text-[#696969] max-w-2xl mx-auto">
            2020 jobs live - 293 added today.
          </p>
        </div>

        {/* Categories Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
          {categories.map((category) => {
            const IconComponent = category.icon;
            return (
              <Card
                key={category.id}
                className="group hover:shadow-lg transition-all duration-300 cursor-pointer border-[#e0e6ed] hover:border-[#1967d2]/20"
              >
                <CardContent className="p-6">
                  <div className="flex items-center space-x-4">
                    {/* Icon */}
                    <div
                      className={`w-12 h-12 rounded-lg bg-gradient-to-r ${category.color} flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}
                    >
                      <IconComponent className="w-6 h-6 text-white" />
                    </div>

                    {/* Content */}
                    <div className="flex-1">
                      <h3 className="font-semibold text-[#202124] mb-1 group-hover:text-[#1967d2] transition-colors">
                        {category.title}
                      </h3>
                      <p className="text-sm text-[#696969]">
                        {category.jobCount} jobs available
                      </p>
                    </div>

                    {/* Arrow */}
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                      <svg
                        className="w-5 h-5 text-[#1967d2]"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M9 5l7 7-7 7"
                        />
                      </svg>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Browse All Categories Button */}
        <div className="text-center mt-12">
          <button className="bg-[#1967d2] hover:bg-[#1557b0] text-white font-medium px-8 py-3 rounded-lg transition-colors">
            Browse All Categories
          </button>
        </div>
      </div>
    </section>
  );
}
