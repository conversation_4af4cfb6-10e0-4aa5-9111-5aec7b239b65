import { JobCard } from "@/components/features/jobs/job-card";
import type { Job } from "@/types/job";

interface FeaturedJobsSectionProps {
  jobs: Job[];
}

export function FeaturedJobsSection({ jobs }: FeaturedJobsSectionProps) {
  // Filter to show only featured jobs
  const featuredJobs = jobs.filter((job) => job.featured);

  return (
    <section className="py-16 lg:py-24 bg-[#f0f5f7]">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold text-[#202124] mb-4">
            Featured Jobs
          </h2>
          <p className="text-lg text-[#696969] max-w-2xl mx-auto">
            Know your worth and find the job that qualify your life
          </p>
        </div>

        {/* Jobs Grid */}
        <div className="grid gap-6 lg:gap-8">
          {featuredJobs.map((job) => (
            <JobCard
              key={job.id}
              job={job}
              className="hover:shadow-lg transition-shadow duration-300"
            />
          ))}
        </div>

        {/* Load More Button */}
        <div className="text-center mt-12">
          <button className="bg-[#1967d2] hover:bg-[#1557b0] text-white font-medium px-8 py-3 rounded-lg transition-colors">
            Load More Listings
          </button>
        </div>
      </div>
    </section>
  );
}
