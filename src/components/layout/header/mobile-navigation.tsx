"use client";

import { But<PERSON> } from "@/components/ui/button";
import { X } from "lucide-react";
import Navigation from "./navigation";
import type { MobileNavigationProps } from "@/types/navigation";

const MobileNavigation = ({
  isOpen,
  onClose,
  items,
}: MobileNavigationProps) => {
  if (!isOpen) return null;

  return (
    <div className="lg:hidden">
      <div
        className="fixed inset-0 z-50 bg-black/20 backdrop-blur-sm"
        onClick={onClose}
      />
      <div className="fixed top-0 right-0 z-50 h-full w-80 bg-white shadow-xl">
        <div className="flex items-center justify-between p-4 border-b">
          <span className="text-lg font-semibold">Menu</span>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-5 h-5" />
          </Button>
        </div>
        <div className="p-4">
          <Navigation items={items} isScrolled={true} isMobile={true} />
        </div>
        {/* Mobile Actions */}
        <div className="p-4 border-t space-y-3">
          <Button variant="outline" className="w-full">
            Sign In
          </Button>
          <Button className="w-full">Post a Job</Button>
        </div>
      </div>
    </div>
  );
};

export default MobileNavigation;
