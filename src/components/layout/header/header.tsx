"use client";

import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ChevronDown, Menu, X } from "lucide-react";
import { NAVIGATION_ITEMS, COMPANY_INFO } from "@/constants/navigation";

const Header = () => {
  const router = useRouter();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 200);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Prevent layout shift when mobile menu opens
  useEffect(() => {
    if (isMobileMenuOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }

    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isMobileMenuOpen]);

  return (
    <header
      className={`w-full transition-all duration-300 fixed top-0 z-50 ${
        isScrolled
          ? "bg-white border-b border-gray-100 shadow-sm"
          : "lg:bg-transparent bg-white lg:border-0 border-b border-gray-100 lg:shadow-none shadow-sm"
      }`}
    >
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16 lg:h-20 lg:gap-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-3 flex-shrink-0">
            {/* Logo Icon */}
            <div className="w-8 h-8 bg-[#1967D2] rounded-lg flex items-center justify-center">
              <span className="text-white font-medium text-sm">
                {COMPANY_INFO.shortName}
              </span>
            </div>
            {/* Logo Text */}
            <span className="text-[25px] font-medium text-[#202124] font-['Jost',sans-serif]">
              {COMPANY_INFO.name}
            </span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8 flex-1">
            {NAVIGATION_ITEMS.map((item) => (
              <div key={item.id} className="relative">
                {item.children && item.children.length > 0 ? (
                  <DropdownMenu modal={false}>
                    <DropdownMenuTrigger className="flex items-center space-x-1 text-[#202124] hover:text-[#1967D2] transition-colors py-2 focus:outline-none font-['Jost',sans-serif] text-[15px]">
                      <span>{item.label}</span>
                      <ChevronDown className="w-3 h-3" />
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                      sideOffset={12}
                      align="start"
                      avoidCollisions={true}
                      collisionPadding={8}
                    >
                      {item.children.map((child, index) => {
                        // Loại bỏ logic showChevron vì navigation chỉ có 1 cấp
                        return (
                          <DropdownMenuItem
                            key={child.href}
                            showChevron={false}
                            onClick={() => router.push(child.href)}
                          >
                            {child.label}
                          </DropdownMenuItem>
                        );
                      })}
                    </DropdownMenuContent>
                  </DropdownMenu>
                ) : (
                  <Link
                    href={item.href}
                    className="text-[#202124] hover:text-[#1967D2] transition-colors font-['Jost',sans-serif] text-[15px]"
                  >
                    {item.label}
                  </Link>
                )}
              </div>
            ))}
          </nav>

          {/* Desktop Actions */}
          <div className="hidden lg:flex items-center space-x-6 flex-shrink-0">
            {/* Upload CV Link */}
            <Link
              href="/upload-cv"
              className="text-[#1967D2] hover:text-[#1967D2]/80 transition-colors font-['Jost',sans-serif] text-[15px] whitespace-nowrap"
            >
              Upload your CV
            </Link>

            {/* Login/Register Button */}
            <Link
              href="/login"
              className="px-4 py-2 bg-[#1967D2]/7 text-[#1967D2] hover:bg-[#1967D2]/10 transition-colors rounded-lg font-['Jost',sans-serif] text-[15px] whitespace-nowrap"
            >
              Login / Register
            </Link>

            {/* Job Post Button */}
            <Link
              href="/post-job"
              className="px-4 py-2 bg-[#1967D2] text-white hover:bg-[#1967D2]/90 transition-colors rounded-lg font-['Jost',sans-serif] text-[15px] whitespace-nowrap"
            >
              Job Post
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <button
            className="lg:hidden p-2 rounded-md text-[#202124] hover:text-[#1967D2] hover:bg-gray-50 transition-colors"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            {isMobileMenuOpen ? (
              <X className="w-6 h-6" />
            ) : (
              <Menu className="w-6 h-6" />
            )}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className="lg:hidden border-t border-gray-100 py-4">
            <nav className="space-y-2">
              {NAVIGATION_ITEMS.map((item) => (
                <div key={item.id}>
                  <Link
                    href={item.href}
                    className="block px-4 py-2 text-[#202124] hover:text-[#1967D2] hover:bg-gray-50 rounded-md transition-colors font-['Jost',sans-serif] text-[15px]"
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {item.label}
                  </Link>
                  {item.children && item.children.length > 0 && (
                    <div className="ml-4 mt-2 space-y-1">
                      {item.children.map((child) => (
                        <Link
                          key={child.href}
                          href={child.href}
                          className="block px-4 py-2 text-sm text-gray-600 hover:text-[#1967D2] hover:bg-gray-50 rounded-md transition-colors"
                          onClick={() => setIsMobileMenuOpen(false)}
                        >
                          {child.label}
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </nav>

            {/* Mobile Actions */}
            <div className="mt-6 pt-4 border-t border-gray-100 space-y-3">
              <Link
                href="/upload-cv"
                className="block px-4 py-2 text-[#1967D2] hover:bg-blue-50 rounded-md transition-colors font-medium"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Upload your CV
              </Link>

              <Link
                href="/login"
                className="block px-4 py-2 text-[#1967D2] bg-[#1967D2]/7 hover:bg-[#1967D2]/10 rounded-md transition-colors font-medium"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Login / Register
              </Link>

              <Link
                href="/post-job"
                className="block mx-4 py-3 bg-[#1967D2] text-white text-center rounded-md hover:bg-[#1967D2]/90 transition-colors font-medium"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Job Post
              </Link>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
