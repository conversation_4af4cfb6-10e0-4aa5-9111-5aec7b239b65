"use client";

import { memo } from "react";
import {
  Card,
  Badge,
  Avatar,
  AvatarFallback,
  AvatarImage,
  Button,
} from "@/components/client";
import {
  Bookmark,
  MapPin,
  Clock,
  DollarSign,
  Building2,
  Briefcase,
} from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import type { Job } from "@/types/job";
import { cn } from "@/lib/utils";

// Màu sắc theo Superio Theme
const COLORS = {
  primary: "#1967D2",
  text: "#202124",
  textMuted: "#696969",
  border: "#ECEDF2",
  fullTime: "#1967D2",
  private: "#34A853",
  urgent: "#F9AB00",
  background: "#FFFFFF",
};

interface JobCardProps {
  job: Job;
  className?: string;
  onBookmark?: (jobId: string) => void;
  onApply?: (jobId: string) => void;
  onViewDetails?: (jobId: string) => void;
}

/**
 * JobCard component theo thiết kế <PERSON> Theme từ Figma
 */
export const JobCard = memo(
  ({ job, className, onBookmark, onApply, onViewDetails }: JobCardProps) => {
    const formatSalary = (salary: Job["salary"]) => {
      const { min, max, currency, period } = salary;
      const minFormatted = new Intl.NumberFormat().format(min);
      const maxFormatted = new Intl.NumberFormat().format(max);
      return `${currency} ${minFormatted} - ${maxFormatted}/${period}`;
    };

    return (
      <Card
        className={cn(
          "hover:shadow-md transition-shadow border border-[#ECEDF2] overflow-hidden",
          "p-0 relative",
          className
        )}
      >
        {/* Logo công ty */}
        <div className="absolute top-5 left-5 w-[70px] h-[70px] bg-[#ecedf2] rounded-lg flex items-center justify-center">
          <Avatar className="w-10 h-10">
            <AvatarImage src={job.company.logo} alt={job.company.name} />
            <AvatarFallback className="bg-[#ecedf2] text-[#696969]">
              <Building2 className="h-6 w-6" />
            </AvatarFallback>
          </Avatar>
        </div>

        {/* Bookmark button */}
        <Button
          variant="ghost"
          size="icon"
          onClick={() => onBookmark?.(job.id)}
          className="absolute top-3 right-3 text-[#696969] hover:text-[#202124]"
        >
          <Bookmark
            className={cn(
              "h-4 w-4",
              job.isBookmarked && "fill-current text-[#1967D2]"
            )}
          />
        </Button>

        {/* Job content */}
        <div className="pl-24 pr-5 pt-5 pb-4">
          {/* Title */}
          <h3
            className="text-lg font-medium text-[#202124] hover:text-[#1967D2] transition-colors cursor-pointer mb-1"
            onClick={() => onViewDetails?.(job.id)}
          >
            {job.title}
          </h3>

          {/* Company */}
          <div className="flex items-center gap-2 mb-3">
            <span className="text-sm text-[#696969]">{job.company.name}</span>
          </div>

          {/* Meta info */}
          <div className="flex flex-wrap items-center gap-4 text-sm text-[#696969] mb-4">
            <div className="flex items-center gap-1">
              <Briefcase className="h-4 w-4" />
              <span>{job.company.name}</span>
            </div>

            <div className="flex items-center gap-1">
              <MapPin className="h-4 w-4" />
              <span>{job.location}</span>
            </div>

            <div className="flex items-center gap-1">
              <Clock className="h-4 w-4" />
              <span>
                {formatDistanceToNow(new Date(job.postedAt), {
                  addSuffix: true,
                })}
              </span>
            </div>

            <div className="flex items-center gap-1">
              <DollarSign className="h-4 w-4" />
              <span>{formatSalary(job.salary)}</span>
            </div>
          </div>

          {/* Tags */}
          <div className="flex flex-wrap gap-2">
            <Badge className="bg-[#1967D2]/15 hover:bg-[#1967D2]/15 text-[#1967D2] rounded-full border-0 font-normal">
              {job.employmentType}
            </Badge>

            {job.featured && (
              <Badge className="bg-[#34A853]/15 hover:bg-[#34A853]/15 text-[#34A853] rounded-full border-0 font-normal">
                Private
              </Badge>
            )}

            {job.urgent && (
              <Badge className="bg-[#F9AB00]/15 hover:bg-[#F9AB00]/15 text-[#F9AB00] rounded-full border-0 font-normal">
                Urgent
              </Badge>
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="border-t border-[#ECEDF2] p-4 flex items-center justify-between">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onViewDetails?.(job.id)}
            className="border-[#ECEDF2] text-[#696969] hover:text-[#1967D2]"
          >
            View Details
          </Button>

          <Button
            size="sm"
            onClick={() => onApply?.(job.id)}
            className="bg-[#1967D2] hover:bg-[#1967D2]/90 text-white"
          >
            Apply Now
          </Button>
        </div>
      </Card>
    );
  }
);
